#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据加载器模块
Data Loader Module

支持多种格式的数据文件加载，包括CSV、Excel、ARFF等格式
"""

import pandas as pd
import numpy as np
import os
from typing import Tuple, Optional, Dict, Any
from sklearn.datasets import load_iris, load_wine, load_breast_cancer
import warnings

warnings.filterwarnings('ignore')

class DataLoader:
    """数据加载器类"""
    
    def __init__(self):
        """初始化数据加载器"""
        self.data = None
        self.target = None
        self.feature_names = None
        self.target_names = None
        self.dataset_info = {}
    
    def load_csv(self, file_path: str, target_column: str = None, 
                 separator: str = ',', header: int = 0) -> Tuple[pd.DataFrame, pd.Series]:
        """
        加载CSV文件
        
        Args:
            file_path: CSV文件路径
            target_column: 目标列名称
            separator: 分隔符
            header: 表头行号
            
        Returns:
            特征数据和目标数据
        """
        try:
            # 读取CSV文件
            df = pd.read_csv(file_path, sep=separator, header=header)
            
            # 处理目标列
            if target_column:
                if target_column in df.columns:
                    self.target = df[target_column]
                    self.data = df.drop(columns=[target_column])
                else:
                    raise ValueError(f"目标列 '{target_column}' 不存在")
            else:
                # 假设最后一列是目标列
                self.target = df.iloc[:, -1]
                self.data = df.iloc[:, :-1]
            
            self.feature_names = list(self.data.columns)
            self.target_names = list(self.target.unique())
            
            self.dataset_info = {
                'file_path': file_path,
                'n_samples': len(self.data),
                'n_features': len(self.feature_names),
                'n_classes': len(self.target_names),
                'feature_names': self.feature_names,
                'target_names': self.target_names
            }
            
            return self.data, self.target
            
        except Exception as e:
            raise Exception(f"加载CSV文件失败: {str(e)}")
    
    def load_excel(self, file_path: str, sheet_name: str = 0,
                   target_column: str = None) -> Tuple[pd.DataFrame, pd.Series]:
        """
        加载Excel文件（支持.xlsx和.xls格式）

        Args:
            file_path: Excel文件路径
            sheet_name: 工作表名称或索引
            target_column: 目标列名称

        Returns:
            特征数据和目标数据
        """
        try:
            # 检查文件扩展名并选择合适的引擎
            file_extension = file_path.lower().split('.')[-1]

            if file_extension == 'xls':
                # 对于.xls文件，使用xlrd引擎
                df = pd.read_excel(file_path, sheet_name=sheet_name, engine='xlrd')
            elif file_extension == 'xlsx':
                # 对于.xlsx文件，使用openpyxl引擎
                df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
            else:
                # 尝试自动检测
                df = pd.read_excel(file_path, sheet_name=sheet_name)

            # 处理目标列
            if target_column:
                if target_column in df.columns:
                    self.target = df[target_column]
                    self.data = df.drop(columns=[target_column])
                else:
                    raise ValueError(f"目标列 '{target_column}' 不存在")
            else:
                # 假设最后一列是目标列
                self.target = df.iloc[:, -1]
                self.data = df.iloc[:, :-1]

            self.feature_names = list(self.data.columns)
            self.target_names = list(self.target.unique())

            self.dataset_info = {
                'file_path': file_path,
                'file_format': file_extension.upper(),
                'sheet_name': sheet_name,
                'n_samples': len(self.data),
                'n_features': len(self.feature_names),
                'n_classes': len(self.target_names),
                'feature_names': self.feature_names,
                'target_names': self.target_names
            }

            return self.data, self.target

        except Exception as e:
            raise Exception(f"加载Excel文件失败: {str(e)}")
    
    def load_sklearn_dataset(self, dataset_name: str) -> Tuple[pd.DataFrame, pd.Series]:
        """
        加载sklearn内置数据集
        
        Args:
            dataset_name: 数据集名称 ('iris', 'wine', 'breast_cancer')
            
        Returns:
            特征数据和目标数据
        """
        try:
            if dataset_name.lower() == 'iris':
                dataset = load_iris()
            elif dataset_name.lower() == 'wine':
                dataset = load_wine()
            elif dataset_name.lower() == 'breast_cancer':
                dataset = load_breast_cancer()
            else:
                raise ValueError(f"不支持的数据集: {dataset_name}")
            
            self.data = pd.DataFrame(dataset.data, columns=dataset.feature_names)
            self.target = pd.Series(dataset.target)
            self.feature_names = list(dataset.feature_names)
            self.target_names = list(dataset.target_names)
            
            self.dataset_info = {
                'dataset_name': dataset_name,
                'n_samples': len(self.data),
                'n_features': len(self.feature_names),
                'n_classes': len(self.target_names),
                'feature_names': self.feature_names,
                'target_names': self.target_names,
                'description': dataset.DESCR
            }
            
            return self.data, self.target
            
        except Exception as e:
            raise Exception(f"加载sklearn数据集失败: {str(e)}")
    
    def get_dataset_info(self) -> Dict[str, Any]:
        """获取数据集信息"""
        return self.dataset_info
    
    def get_data_summary(self) -> Dict[str, Any]:
        """获取数据摘要信息"""
        if self.data is None:
            return {}
        
        summary = {
            'shape': self.data.shape,
            'dtypes': self.data.dtypes.to_dict(),
            'missing_values': self.data.isnull().sum().to_dict(),
            'statistics': self.data.describe().to_dict()
        }
        
        return summary
