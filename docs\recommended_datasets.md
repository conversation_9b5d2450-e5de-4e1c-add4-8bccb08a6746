# 推荐数据集列表

## UCI机器学习数据集

### 1. Iris（鸢尾花数据集）
- **描述**: 经典的分类数据集，包含3种鸢尾花的4个特征
- **样本数**: 150
- **特征数**: 4
- **类别数**: 3
- **获取方式**: 系统内置，或从 https://archive.ics.uci.edu/ml/datasets/iris

### 2. Wine（葡萄酒数据集）
- **描述**: 意大利葡萄酒的化学分析数据
- **样本数**: 178
- **特征数**: 13
- **类别数**: 3
- **获取方式**: 系统内置，或从 https://archive.ics.uci.edu/ml/datasets/wine

### 3. Breast Cancer Wisconsin（乳腺癌数据集）
- **描述**: 乳腺癌诊断数据，用于良性/恶性分类
- **样本数**: 569
- **特征数**: 30
- **类别数**: 2
- **获取方式**: 系统内置，或从 https://archive.ics.uci.edu/ml/datasets/Breast+Cancer+Wisconsin+(Diagnostic)

### 4. Heart Disease（心脏病数据集）
- **描述**: 心脏病诊断相关的医学数据
- **样本数**: 303
- **特征数**: 13
- **类别数**: 2
- **下载链接**: https://archive.ics.uci.edu/ml/datasets/Heart+Disease

### 5. Diabetes（糖尿病数据集）
- **描述**: 皮马印第安人糖尿病数据
- **样本数**: 768
- **特征数**: 8
- **类别数**: 2
- **下载链接**: https://www.kaggle.com/datasets/uciml/pima-indians-diabetes-database

### 6. Sonar（声纳数据集）
- **描述**: 声纳信号分类，区分岩石和金属
- **样本数**: 208
- **特征数**: 60
- **类别数**: 2
- **下载链接**: https://archive.ics.uci.edu/ml/datasets/Connectionist+Bench+(Sonar,+Mines+vs.+Rocks)

### 7. Ionosphere（电离层数据集）
- **描述**: 雷达数据，用于电离层结构分类
- **样本数**: 351
- **特征数**: 34
- **类别数**: 2
- **下载链接**: https://archive.ics.uci.edu/ml/datasets/Ionosphere

### 8. Glass（玻璃数据集）
- **描述**: 玻璃类型识别数据
- **样本数**: 214
- **特征数**: 9
- **类别数**: 6
- **下载链接**: https://archive.ics.uci.edu/ml/datasets/Glass+Identification

## 获取数据集的方法

### 方法1: 直接下载CSV文件
1. 访问UCI机器学习数据库: https://archive.ics.uci.edu/ml/index.php
2. 搜索数据集名称
3. 下载CSV格式文件
4. 将文件放入项目的`data/`目录

### 方法2: 使用Python脚本下载
```python
import pandas as pd
from sklearn.datasets import fetch_openml

# 下载OpenML数据集
data = fetch_openml('heart-statlog', version=1, as_frame=True)
df = pd.concat([data.data, data.target], axis=1)
df.to_csv('data/heart_disease.csv', index=False)
```

### 方法3: Kaggle数据集
1. 注册Kaggle账号: https://www.kaggle.com/
2. 搜索相关数据集
3. 下载CSV文件

## 数据集文件格式要求

### CSV格式示例
```csv
feature1,feature2,feature3,target
1.2,3.4,5.6,class_A
2.3,4.5,6.7,class_B
```

### Excel格式示例
- 第一行为列名（特征名称）
- 最后一列为目标变量
- 数据从第二行开始

## 推荐的测试数据集组合

### 小规模数据集（适合快速测试）
- Iris (150样本, 4特征)
- Wine (178样本, 13特征)

### 中等规模数据集（适合详细分析）
- Heart Disease (303样本, 13特征)
- Diabetes (768样本, 8特征)

### 大规模数据集（适合性能测试）
- Breast Cancer (569样本, 30特征)
- Sonar (208样本, 60特征)

## 数据集特点分析

| 数据集 | 样本数 | 特征数 | 类别数 | 难度 | 适用场景 |
|--------|--------|--------|--------|------|----------|
| Iris | 150 | 4 | 3 | 简单 | 入门学习 |
| Wine | 178 | 13 | 3 | 中等 | 多分类测试 |
| Heart Disease | 303 | 13 | 2 | 中等 | 医学数据分析 |
| Diabetes | 768 | 8 | 2 | 中等 | 二分类问题 |
| Breast Cancer | 569 | 30 | 2 | 中等 | 高维特征选择 |
| Sonar | 208 | 60 | 2 | 困难 | 高维小样本 |
| Ionosphere | 351 | 34 | 2 | 困难 | 特征约简效果明显 |

## 使用建议

1. **初学者**: 从Iris数据集开始，理解基本流程
2. **进阶用户**: 使用Wine或Heart Disease测试不同算法
3. **研究目的**: 使用Sonar或Ionosphere验证算法有效性
4. **实际应用**: 使用Diabetes或Breast Cancer模拟真实场景
