# 属性约简算法研究系统

## 项目简介
本项目是一个数据挖掘系统，专注于属性约简算法的研究与应用。系统实现了多种属性约简方法，并通过分类算法评估约简效果，帮助用户理解和应用特征选择技术。

## 项目目标
- 巩固已学知识，扩展新知识
- 提高问题分析和处理能力
- 研究数据预处理中的属性约简技术
- 对比分析不同约简方法的优劣

## 功能特点
1. **数据预处理**: 支持CSV、Excel(.xlsx/.xls)、UCI数据集的加载和预处理
2. **属性约简**: 实现多种约简算法（PCA、信息增益、相关性分析等）
3. **分类评估**: 集成多种分类算法评估约简效果
4. **可视化界面**: 提供友好的图形用户界面
5. **性能分析**: 详细的性能评估和对比分析
6. **丰富数据集**: 内置多个经典数据集，支持一键下载

## 安装说明
```bash
# 安装依赖包
pip install -r requirements.txt

# 下载推荐数据集
python download_datasets.py
```

## 使用方法
```bash
# 启动GUI界面
python main.py

# 运行演示脚本
python demo.py
```

## 支持的数据格式
- **CSV文件**: 标准逗号分隔值文件
- **Excel文件**: 支持.xlsx和.xls格式
- **内置数据集**: Iris, Wine, Breast Cancer

## 可用数据集
运行`python download_datasets.py`后，您将获得以下数据集：
- Heart Disease (270样本, 13特征, 2类别)
- Diabetes (768样本, 8特征, 2类别)
- Ionosphere (351样本, 34特征, 2类别)
- Sonar (208样本, 60特征, 2类别)
- Glass (214样本, 9特征, 6类别)
- Iris示例Excel文件 (.xlsx/.xls格式)

## 项目结构
```
├── data/                   # 数据文件夹
├── src/                    # 源代码
│   ├── data_preprocessing/ # 数据预处理模块
│   ├── feature_reduction/  # 属性约简算法
│   ├── classification/     # 分类算法
│   ├── evaluation/         # 性能评估
│   └── gui/               # 图形界面
├── tests/                 # 测试文件
├── docs/                  # 文档
├── main.py               # 主程序入口
├── demo.py               # 演示脚本
└── download_datasets.py  # 数据集下载工具
```

## 作者
数据挖掘课程项目
