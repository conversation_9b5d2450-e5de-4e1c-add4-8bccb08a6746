# 数据集使用指南

## 快速开始

### 1. 下载数据集
```bash
python download_datasets.py
```

### 2. 查看可用数据集
运行下载脚本后，您将在`data/`目录中找到以下数据集：

| 文件名 | 格式 | 样本数 | 特征数 | 类别数 | 描述 |
|--------|------|--------|--------|--------|------|
| `heart_disease.csv` | CSV | 270 | 13 | 2 | 心脏病诊断数据 |
| `diabetes.csv` | CSV | 768 | 8 | 2 | 糖尿病诊断数据 |
| `ionosphere.csv` | CSV | 351 | 34 | 2 | 电离层雷达数据 |
| `sonar.csv` | CSV | 208 | 60 | 2 | 声纳信号分类 |
| `glass.csv` | CSV | 214 | 9 | 6 | 玻璃类型识别 |
| `iris_sample.xlsx` | Excel | 150 | 4 | 3 | 鸢尾花数据（XLSX格式） |
| `iris_sample.xls` | Excel | 150 | 4 | 3 | 鸢尾花数据（XLS格式） |

## 数据集详细说明

### 1. Heart Disease（心脏病数据集）
- **用途**: 二分类问题，预测是否患有心脏病
- **特征**: 年龄、性别、胸痛类型、血压等医学指标
- **适用场景**: 医学数据分析、二分类算法测试
- **约简建议**: 使用相关性分析去除冗余医学指标

### 2. Diabetes（糖尿病数据集）
- **用途**: 二分类问题，预测是否患有糖尿病
- **特征**: 怀孕次数、血糖浓度、血压、BMI等
- **适用场景**: 医学预测、特征选择研究
- **约简建议**: 信息增益方法效果较好

### 3. Ionosphere（电离层数据集）
- **用途**: 二分类问题，雷达信号分类
- **特征**: 34个连续数值特征
- **适用场景**: 高维特征约简、PCA效果验证
- **约简建议**: PCA可以显著降维，保持较好性能

### 4. Sonar（声纳数据集）
- **用途**: 二分类问题，区分岩石和金属物体
- **特征**: 60个声纳信号频率特征
- **适用场景**: 高维小样本问题、约简算法性能测试
- **约简建议**: 各种约简方法都有明显效果

### 5. Glass（玻璃数据集）
- **用途**: 多分类问题，识别玻璃类型
- **特征**: 9个化学成分特征
- **适用场景**: 多分类问题、化学数据分析
- **约简建议**: 信息增益和相关性分析都适用

## 使用方法

### 方法1: GUI界面使用
1. 启动程序：`python main.py`
2. 在"数据处理"标签页点击"浏览"
3. 选择`data/`目录中的任意文件
4. 设置目标列（通常是最后一列）
5. 点击"加载数据"

### 方法2: 编程接口使用
```python
from src.data_preprocessing.data_loader import DataLoader

# 加载CSV数据
loader = DataLoader()
data, target = loader.load_csv('data/heart_disease.csv')

# 加载Excel数据
data, target = loader.load_excel('data/iris_sample.xlsx')
```

## 数据格式要求

### CSV文件格式
```csv
feature1,feature2,feature3,target
1.2,3.4,5.6,class_A
2.3,4.5,6.7,class_B
```

### Excel文件格式
- 支持`.xlsx`和`.xls`格式
- 第一行为列名
- 数据从第二行开始
- 目标列可以是任意列（建议放在最后）

## 推荐的实验组合

### 入门级实验
1. **数据集**: Iris (内置) 或 `iris_sample.xlsx`
2. **约简方法**: PCA (2个主成分)
3. **分类器**: 决策树
4. **目标**: 理解基本流程

### 进阶实验
1. **数据集**: `heart_disease.csv`
2. **约简方法**: 信息增益 + 相关性分析
3. **分类器**: 随机森林 + SVM
4. **目标**: 对比不同约简方法

### 高级实验
1. **数据集**: `sonar.csv` (高维小样本)
2. **约简方法**: PCA + 信息增益 + 相关性分析
3. **分类器**: 全部6种分类器
4. **目标**: 验证约简在高维数据上的效果

## 性能基准

基于我们的测试，以下是各数据集的预期性能：

| 数据集 | 原始准确率 | PCA后准确率 | 信息增益后准确率 | 相关性分析后准确率 |
|--------|------------|-------------|------------------|-------------------|
| Iris | ~95% | ~92% | ~96% | ~93% |
| Heart Disease | ~85% | ~82% | ~87% | ~84% |
| Diabetes | ~78% | ~75% | ~80% | ~77% |

## 常见问题

### Q: 如何添加自己的数据集？
A: 将CSV或Excel文件放入`data/`目录，确保格式正确即可。

### Q: 目标列应该放在哪里？
A: 建议放在最后一列，系统会自动识别。也可以手动指定。

### Q: 支持哪些数据类型？
A: 支持数值型和分类型特征，系统会自动处理编码。

### Q: 如何处理缺失值？
A: 系统提供多种缺失值处理策略：均值、中位数、众数填充。

### Q: 数据集太大怎么办？
A: 可以使用数据采样，或者选择较小的数据集进行实验。

## 扩展数据集

如果您需要更多数据集，可以：

1. **访问UCI数据库**: https://archive.ics.uci.edu/ml/
2. **访问Kaggle**: https://www.kaggle.com/datasets
3. **使用sklearn内置数据集**: 修改`data_loader.py`添加更多内置数据集
4. **创建合成数据集**: 使用sklearn的`make_classification`等函数

## 数据集引用

如果您在研究中使用这些数据集，请引用相应的来源：

- **UCI数据集**: Dua, D. and Graff, C. (2019). UCI Machine Learning Repository
- **Heart Disease**: Detrano, R. et al. (1989)
- **Diabetes**: Smith, J.W. et al. (1988)
- **其他数据集**: 请查看UCI数据库中的具体引用信息
