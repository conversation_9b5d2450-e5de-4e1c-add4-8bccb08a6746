# 属性约简算法研究系统用户手册

## 目录
1. [系统简介](#系统简介)
2. [安装说明](#安装说明)
3. [快速开始](#快速开始)
4. [功能详解](#功能详解)
5. [使用示例](#使用示例)
6. [常见问题](#常见问题)

## 系统简介

属性约简算法研究系统是一个专门用于数据挖掘中特征选择和降维的综合性工具。系统集成了多种属性约简算法，并提供了完整的分类评估框架，帮助用户理解和应用特征选择技术。

### 主要功能
- **数据预处理**: 支持多种数据格式的加载和预处理
- **属性约简**: 实现PCA、信息增益、相关性分析等约简算法
- **分类评估**: 集成多种分类算法评估约简效果
- **性能分析**: 提供详细的性能评估和对比分析
- **可视化界面**: 友好的图形用户界面

### 支持的算法

#### 属性约简算法
1. **主成分分析(PCA)**: 基于方差最大化的线性降维
2. **信息增益**: 基于信息论的特征选择
3. **相关性分析**: 基于特征间相关性的特征过滤

#### 分类算法
1. **决策树**: 基于树结构的分类算法
2. **随机森林**: 集成学习算法
3. **支持向量机(SVM)**: 基于最大间隔的分类算法
4. **朴素贝叶斯**: 基于概率的分类算法
5. **K近邻(KNN)**: 基于距离的分类算法
6. **逻辑回归**: 线性分类算法

## 安装说明

### 系统要求
- Python 3.7 或更高版本
- 操作系统: Windows, macOS, Linux

### 安装步骤

1. **克隆或下载项目**
   ```bash
   git clone <repository_url>
   cd Feature-Reduction-and-Classification-System
   ```

2. **安装依赖包**
   ```bash
   pip install -r requirements.txt
   ```

3. **验证安装**
   ```bash
   python demo.py
   ```

### 依赖包说明
- `numpy`: 数值计算库
- `pandas`: 数据处理库
- `scikit-learn`: 机器学习库
- `matplotlib`: 绘图库
- `seaborn`: 统计绘图库
- `tkinter`: GUI库（Python内置）

## 快速开始

### 启动图形界面
```bash
python main.py
```

### 命令行演示
```bash
python demo.py
```

### 基本使用流程
1. **加载数据**: 选择数据文件或内置数据集
2. **数据预处理**: 处理缺失值、标准化数据
3. **属性约简**: 选择约简算法并设置参数
4. **分类评估**: 运行分类算法评估约简效果
5. **结果分析**: 查看性能报告和可视化结果

## 功能详解

### 数据处理模块

#### 数据加载
- **支持格式**: CSV, Excel, 内置数据集
- **内置数据集**: Iris, Wine, Breast Cancer
- **自动检测**: 特征类型、缺失值、数据分布

#### 数据预处理
- **缺失值处理**: 均值、中位数、众数填充
- **数据标准化**: 标准化、最小-最大归一化
- **分类编码**: 独热编码、标签编码

### 属性约简模块

#### PCA约简
- **参数设置**:
  - `n_components`: 主成分数量
  - `variance_threshold`: 方差阈值（自动选择时使用）
- **输出信息**:
  - 解释方差比
  - 特征重要性
  - 累积方差比

#### 信息增益约简
- **参数设置**:
  - `k`: 选择的特征数量
  - `threshold`: 信息增益阈值
- **输出信息**:
  - 特征得分
  - 特征排名
  - 选择的特征列表

#### 相关性分析约简
- **参数设置**:
  - `correlation_threshold`: 相关性阈值
  - `method`: 相关性计算方法（Pearson, Spearman）
- **输出信息**:
  - 相关性矩阵
  - 移除的特征
  - 保留的特征

### 分类评估模块

#### 分类器设置
- **测试集比例**: 默认30%
- **交叉验证**: 默认5折交叉验证
- **评估指标**: 准确率、精确率、召回率、F1分数

#### 性能评估
- **比较分析**: 不同约简方法的性能对比
- **效率分析**: 特征数量与性能的关系
- **时间分析**: 训练时间对比

## 使用示例

### 示例1: 使用GUI界面

1. 启动程序: `python main.py`
2. 在"数据处理"标签页中选择内置数据集"iris"
3. 点击"加载数据"
4. 点击"预处理数据"
5. 切换到"属性约简"标签页
6. 设置PCA主成分数量为2，点击"执行PCA"
7. 设置信息增益选择特征数为2，点击"执行信息增益"
8. 切换到"分类算法"标签页，点击"开始分类评估"
9. 切换到"性能评估"标签页，点击"生成性能报告"

### 示例2: 编程接口使用

```python
from src.data_preprocessing.data_loader import DataLoader
from src.feature_reduction.pca_reducer import PCAReducer
from src.classification.classifiers import ClassifierEnsemble

# 加载数据
loader = DataLoader()
data, target = loader.load_sklearn_dataset('iris')

# PCA约简
pca_reducer = PCAReducer(n_components=2)
reduced_data = pca_reducer.fit_transform(data, target)

# 分类评估
classifier = ClassifierEnsemble()
results = classifier.train_and_evaluate(reduced_data, target)

print(results)
```

## 常见问题

### Q1: 程序启动失败
**A**: 检查Python版本和依赖包安装情况
```bash
python --version
pip list
```

### Q2: 数据加载失败
**A**: 确认文件格式和路径正确，检查文件编码

### Q3: 内存不足
**A**: 对于大数据集，建议：
- 使用数据采样
- 增加系统内存
- 使用批处理模式

### Q4: 可视化图表不显示
**A**: 检查matplotlib配置：
```python
import matplotlib
matplotlib.use('TkAgg')  # 或其他后端
```

### Q5: 约简效果不理想
**A**: 尝试：
- 调整算法参数
- 使用不同的约简方法
- 检查数据预处理步骤

## 技术支持

如有问题或建议，请联系开发团队或查看项目文档。

---

**版本**: 1.0  
**更新日期**: 2025-06-21  
**作者**: 220521309
