#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口GUI模块
Main Window GUI Module
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional
import os
import sys

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data_preprocessing.data_loader import DataLoader
from data_preprocessing.data_cleaner import DataCleaner
from feature_reduction.pca_reducer import PCAReducer
from feature_reduction.information_gain_reducer import InformationGainReducer
from feature_reduction.correlation_reducer import CorrelationReducer
from classification.classifiers import ClassifierEnsemble
from evaluation.performance_evaluator import PerformanceEvaluator

class MainWindow:
    """主窗口类"""
    
    def __init__(self, root):
        """初始化主窗口"""
        self.root = root
        self.root.title("属性约简算法研究系统")
        self.root.geometry("1200x800")
        
        # 数据存储
        self.data_loader = DataLoader()
        self.data_cleaner = DataCleaner()
        self.current_data = None
        self.current_target = None
        self.reduced_datasets = {}
        self.evaluation_results = {}
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个标签页
        self.create_data_tab()
        self.create_reduction_tab()
        self.create_classification_tab()
        self.create_evaluation_tab()
        self.create_results_tab()
        
    def create_data_tab(self):
        """创建数据处理标签页"""
        data_frame = ttk.Frame(self.notebook)
        self.notebook.add(data_frame, text="数据处理")
        
        # 数据加载区域
        load_frame = ttk.LabelFrame(data_frame, text="数据加载", padding=10)
        load_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 文件选择
        ttk.Label(load_frame, text="选择数据文件:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.file_path_var = tk.StringVar()
        ttk.Entry(load_frame, textvariable=self.file_path_var, width=50).grid(row=0, column=1, padx=5, pady=2)
        ttk.Button(load_frame, text="浏览", command=self.browse_file).grid(row=0, column=2, padx=5, pady=2)
        
        # 目标列选择
        ttk.Label(load_frame, text="目标列:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.target_column_var = tk.StringVar()
        self.target_column_combo = ttk.Combobox(load_frame, textvariable=self.target_column_var, width=20)
        self.target_column_combo.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        # 内置数据集选择
        ttk.Label(load_frame, text="或选择内置数据集:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.dataset_var = tk.StringVar()
        dataset_combo = ttk.Combobox(load_frame, textvariable=self.dataset_var, 
                                   values=['iris', 'wine', 'breast_cancer'], width=20)
        dataset_combo.grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
        
        # 加载按钮
        ttk.Button(load_frame, text="加载数据", command=self.load_data).grid(row=3, column=1, pady=10)
        
        # 数据预处理区域
        preprocess_frame = ttk.LabelFrame(data_frame, text="数据预处理", padding=10)
        preprocess_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 预处理选项
        self.missing_strategy_var = tk.StringVar(value='mean')
        ttk.Label(preprocess_frame, text="缺失值处理:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Combobox(preprocess_frame, textvariable=self.missing_strategy_var,
                    values=['mean', 'median', 'most_frequent'], width=15).grid(row=0, column=1, padx=5, pady=2)
        
        self.normalize_method_var = tk.StringVar(value='standard')
        ttk.Label(preprocess_frame, text="标准化方法:").grid(row=0, column=2, sticky=tk.W, pady=2)
        ttk.Combobox(preprocess_frame, textvariable=self.normalize_method_var,
                    values=['standard', 'minmax'], width=15).grid(row=0, column=3, padx=5, pady=2)
        
        ttk.Button(preprocess_frame, text="预处理数据", command=self.preprocess_data).grid(row=1, column=1, pady=10)
        
        # 数据信息显示区域
        info_frame = ttk.LabelFrame(data_frame, text="数据信息", padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.data_info_text = scrolledtext.ScrolledText(info_frame, height=15)
        self.data_info_text.pack(fill=tk.BOTH, expand=True)
        
    def create_reduction_tab(self):
        """创建属性约简标签页"""
        reduction_frame = ttk.Frame(self.notebook)
        self.notebook.add(reduction_frame, text="属性约简")
        
        # PCA约简
        pca_frame = ttk.LabelFrame(reduction_frame, text="主成分分析(PCA)", padding=10)
        pca_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(pca_frame, text="主成分数量:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.pca_components_var = tk.StringVar()
        ttk.Entry(pca_frame, textvariable=self.pca_components_var, width=10).grid(row=0, column=1, padx=5, pady=2)
        ttk.Label(pca_frame, text="(留空自动选择)").grid(row=0, column=2, sticky=tk.W, pady=2)
        
        ttk.Label(pca_frame, text="方差阈值:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.pca_variance_var = tk.StringVar(value='0.95')
        ttk.Entry(pca_frame, textvariable=self.pca_variance_var, width=10).grid(row=1, column=1, padx=5, pady=2)
        
        ttk.Button(pca_frame, text="执行PCA", command=self.apply_pca).grid(row=2, column=1, pady=10)
        
        # 信息增益约简
        ig_frame = ttk.LabelFrame(reduction_frame, text="信息增益", padding=10)
        ig_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(ig_frame, text="选择特征数:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.ig_k_var = tk.StringVar(value='10')
        ttk.Entry(ig_frame, textvariable=self.ig_k_var, width=10).grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Button(ig_frame, text="执行信息增益", command=self.apply_information_gain).grid(row=1, column=1, pady=10)
        
        # 相关性分析约简
        corr_frame = ttk.LabelFrame(reduction_frame, text="相关性分析", padding=10)
        corr_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(corr_frame, text="相关性阈值:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.corr_threshold_var = tk.StringVar(value='0.9')
        ttk.Entry(corr_frame, textvariable=self.corr_threshold_var, width=10).grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Label(corr_frame, text="相关性方法:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.corr_method_var = tk.StringVar(value='pearson')
        ttk.Combobox(corr_frame, textvariable=self.corr_method_var,
                    values=['pearson', 'spearman'], width=15).grid(row=1, column=1, padx=5, pady=2)
        
        ttk.Button(corr_frame, text="执行相关性分析", command=self.apply_correlation).grid(row=2, column=1, pady=10)
        
        # 约简结果显示
        result_frame = ttk.LabelFrame(reduction_frame, text="约简结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.reduction_result_text = scrolledtext.ScrolledText(result_frame, height=10)
        self.reduction_result_text.pack(fill=tk.BOTH, expand=True)
        
    def create_classification_tab(self):
        """创建分类算法标签页"""
        classification_frame = ttk.Frame(self.notebook)
        self.notebook.add(classification_frame, text="分类算法")
        
        # 分类器选择
        classifier_frame = ttk.LabelFrame(classification_frame, text="分类器设置", padding=10)
        classifier_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(classifier_frame, text="测试集比例:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.test_size_var = tk.StringVar(value='0.3')
        ttk.Entry(classifier_frame, textvariable=self.test_size_var, width=10).grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Label(classifier_frame, text="交叉验证折数:").grid(row=0, column=2, sticky=tk.W, pady=2)
        self.cv_folds_var = tk.StringVar(value='5')
        ttk.Entry(classifier_frame, textvariable=self.cv_folds_var, width=10).grid(row=0, column=3, padx=5, pady=2)
        
        ttk.Button(classifier_frame, text="开始分类评估", command=self.run_classification).grid(row=1, column=1, pady=10)
        
        # 分类结果显示
        result_frame = ttk.LabelFrame(classification_frame, text="分类结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.classification_result_text = scrolledtext.ScrolledText(result_frame, height=20)
        self.classification_result_text.pack(fill=tk.BOTH, expand=True)
        
    def create_evaluation_tab(self):
        """创建性能评估标签页"""
        evaluation_frame = ttk.Frame(self.notebook)
        self.notebook.add(evaluation_frame, text="性能评估")
        
        # 评估控制
        control_frame = ttk.LabelFrame(evaluation_frame, text="评估控制", padding=10)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(control_frame, text="生成性能报告", command=self.generate_performance_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="导出结果", command=self.export_results).pack(side=tk.LEFT, padx=5)
        
        # 评估结果显示
        result_frame = ttk.LabelFrame(evaluation_frame, text="评估结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.evaluation_result_text = scrolledtext.ScrolledText(result_frame, height=25)
        self.evaluation_result_text.pack(fill=tk.BOTH, expand=True)
        
    def create_results_tab(self):
        """创建结果展示标签页"""
        results_frame = ttk.Frame(self.notebook)
        self.notebook.add(results_frame, text="结果展示")
        
        # 图表控制
        chart_frame = ttk.LabelFrame(results_frame, text="图表生成", padding=10)
        chart_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(chart_frame, text="性能对比图", command=self.show_performance_chart).pack(side=tk.LEFT, padx=5)
        ttk.Button(chart_frame, text="约简效率图", command=self.show_efficiency_chart).pack(side=tk.LEFT, padx=5)
        
        # 结果摘要显示
        summary_frame = ttk.LabelFrame(results_frame, text="结果摘要", padding=10)
        summary_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.results_summary_text = scrolledtext.ScrolledText(summary_frame, height=25)
        self.results_summary_text.pack(fill=tk.BOTH, expand=True)

    def browse_file(self):
        """浏览文件"""
        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[
                ("CSV files", "*.csv"),
                ("Excel XLSX files", "*.xlsx"),
                ("Excel XLS files", "*.xls"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.file_path_var.set(file_path)

            # 尝试读取列名
            try:
                if file_path.endswith('.csv'):
                    df = pd.read_csv(file_path, nrows=0)
                    self.target_column_combo['values'] = list(df.columns)
                elif file_path.endswith(('.xlsx', '.xls')):
                    # 对于Excel文件，读取第一行作为列名
                    if file_path.endswith('.xls'):
                        df = pd.read_excel(file_path, nrows=0, engine='xlrd')
                    else:
                        df = pd.read_excel(file_path, nrows=0, engine='openpyxl')
                    self.target_column_combo['values'] = list(df.columns)
            except Exception as e:
                print(f"读取文件列名失败: {str(e)}")
                # 如果读取失败，清空下拉列表
                self.target_column_combo['values'] = []

    def load_data(self):
        """加载数据"""
        try:
            file_path = self.file_path_var.get()
            dataset_name = self.dataset_var.get()
            target_column = self.target_column_var.get()

            if dataset_name:
                # 加载内置数据集
                self.current_data, self.current_target = self.data_loader.load_sklearn_dataset(dataset_name)
                info_text = f"成功加载内置数据集: {dataset_name}\n"
            elif file_path:
                # 加载文件
                if file_path.endswith('.csv'):
                    self.current_data, self.current_target = self.data_loader.load_csv(
                        file_path, target_column if target_column else None
                    )
                elif file_path.endswith(('.xlsx', '.xls')):
                    self.current_data, self.current_target = self.data_loader.load_excel(
                        file_path, target_column=target_column if target_column else None
                    )
                else:
                    raise ValueError("不支持的文件格式，请使用CSV或Excel文件")
                info_text = f"成功加载文件: {file_path}\n"
            else:
                messagebox.showerror("错误", "请选择数据文件或内置数据集")
                return

            # 显示数据信息
            dataset_info = self.data_loader.get_dataset_info()
            data_summary = self.data_loader.get_data_summary()

            info_text += f"数据形状: {dataset_info['n_samples']} 样本, {dataset_info['n_features']} 特征\n"
            info_text += f"类别数量: {dataset_info['n_classes']}\n"
            info_text += f"特征名称: {', '.join(dataset_info['feature_names'][:10])}{'...' if len(dataset_info['feature_names']) > 10 else ''}\n"
            info_text += f"目标类别: {', '.join(map(str, dataset_info['target_names']))}\n\n"

            if data_summary:
                info_text += "数据统计信息:\n"
                info_text += f"缺失值: {sum(data_summary['missing_values'].values())} 个\n"
                info_text += f"数据类型: {len(data_summary['dtypes'])} 种\n"

            self.data_info_text.delete(1.0, tk.END)
            self.data_info_text.insert(tk.END, info_text)

            messagebox.showinfo("成功", "数据加载成功！")

        except Exception as e:
            messagebox.showerror("错误", f"数据加载失败: {str(e)}")

    def preprocess_data(self):
        """预处理数据"""
        if self.current_data is None:
            messagebox.showerror("错误", "请先加载数据")
            return

        try:
            # 重置清洗日志
            self.data_cleaner.reset_log()

            # 处理缺失值
            missing_strategy = self.missing_strategy_var.get()
            self.current_data = self.data_cleaner.handle_missing_values(self.current_data, missing_strategy)

            # 编码分类变量
            self.current_data, self.current_target = self.data_cleaner.encode_categorical(
                self.current_data, self.current_target
            )

            # 标准化数据
            normalize_method = self.normalize_method_var.get()
            self.current_data = self.data_cleaner.normalize_data(self.current_data, normalize_method)

            # 显示预处理日志
            log_text = "数据预处理完成:\n"
            for log_entry in self.data_cleaner.get_cleaning_log():
                log_text += f"- {log_entry}\n"

            log_text += f"\n预处理后数据形状: {self.current_data.shape}\n"

            self.data_info_text.insert(tk.END, f"\n{log_text}")

            messagebox.showinfo("成功", "数据预处理完成！")

        except Exception as e:
            messagebox.showerror("错误", f"数据预处理失败: {str(e)}")

    def apply_pca(self):
        """应用PCA约简"""
        if self.current_data is None:
            messagebox.showerror("错误", "请先加载并预处理数据")
            return

        try:
            # 获取参数
            n_components = None
            if self.pca_components_var.get():
                n_components = int(self.pca_components_var.get())

            variance_threshold = float(self.pca_variance_var.get())

            # 执行PCA
            pca_reducer = PCAReducer(n_components=n_components, variance_threshold=variance_threshold)
            reduced_data = pca_reducer.fit_transform(self.current_data, self.current_target)

            # 保存结果
            self.reduced_datasets['PCA'] = reduced_data

            # 显示结果
            info = pca_reducer.get_reduction_info()
            result_text = f"PCA约简完成:\n"
            result_text += f"原始特征数: {info['original_features']}\n"
            result_text += f"约简后特征数: {info['reduced_features']}\n"
            result_text += f"约简率: {info['reduction_ratio']:.1%}\n"
            result_text += f"解释方差比: {info['total_variance_explained']:.3f}\n\n"

            self.reduction_result_text.insert(tk.END, result_text)

            messagebox.showinfo("成功", "PCA约简完成！")

        except Exception as e:
            messagebox.showerror("错误", f"PCA约简失败: {str(e)}")

    def apply_information_gain(self):
        """应用信息增益约简"""
        if self.current_data is None or self.current_target is None:
            messagebox.showerror("错误", "请先加载并预处理数据")
            return

        try:
            # 获取参数
            k = int(self.ig_k_var.get())

            # 执行信息增益
            ig_reducer = InformationGainReducer(k=k)
            reduced_data = ig_reducer.fit_transform(self.current_data, self.current_target)

            # 保存结果
            self.reduced_datasets['Information_Gain'] = reduced_data

            # 显示结果
            info = ig_reducer.get_reduction_info()
            selected_features = ig_reducer.get_selected_features()

            result_text = f"信息增益约简完成:\n"
            result_text += f"原始特征数: {info['original_features']}\n"
            result_text += f"约简后特征数: {info['reduced_features']}\n"
            result_text += f"约简率: {info['reduction_ratio']:.1%}\n"
            result_text += f"选择的特征: {', '.join(selected_features[:5])}{'...' if len(selected_features) > 5 else ''}\n\n"

            self.reduction_result_text.insert(tk.END, result_text)

            messagebox.showinfo("成功", "信息增益约简完成！")

        except Exception as e:
            messagebox.showerror("错误", f"信息增益约简失败: {str(e)}")

    def apply_correlation(self):
        """应用相关性分析约简"""
        if self.current_data is None:
            messagebox.showerror("错误", "请先加载并预处理数据")
            return

        try:
            # 获取参数
            correlation_threshold = float(self.corr_threshold_var.get())
            method = self.corr_method_var.get()

            # 执行相关性分析
            corr_reducer = CorrelationReducer(
                correlation_threshold=correlation_threshold,
                method=method
            )
            reduced_data = corr_reducer.fit_transform(self.current_data, self.current_target)

            # 保存结果
            self.reduced_datasets['Correlation'] = reduced_data

            # 显示结果
            info = corr_reducer.get_reduction_info()
            selected_features = corr_reducer.get_selected_features()
            removed_features = corr_reducer.get_removed_features()

            result_text = f"相关性分析约简完成:\n"
            result_text += f"原始特征数: {info['original_features']}\n"
            result_text += f"约简后特征数: {info['reduced_features']}\n"
            result_text += f"移除特征数: {info['removed_features']}\n"
            result_text += f"约简率: {info['reduction_ratio']:.1%}\n"
            result_text += f"保留的特征: {', '.join(selected_features[:5])}{'...' if len(selected_features) > 5 else ''}\n\n"

            self.reduction_result_text.insert(tk.END, result_text)

            messagebox.showinfo("成功", "相关性分析约简完成！")

        except Exception as e:
            messagebox.showerror("错误", f"相关性分析约简失败: {str(e)}")

    def run_classification(self):
        """运行分类评估"""
        if not self.reduced_datasets and self.current_data is None:
            messagebox.showerror("错误", "请先进行属性约简或加载数据")
            return

        if self.current_target is None:
            messagebox.showerror("错误", "缺少目标变量")
            return

        try:
            # 获取参数
            test_size = float(self.test_size_var.get())
            cv_folds = int(self.cv_folds_var.get())

            # 创建分类器集成
            classifier_ensemble = ClassifierEnsemble()

            # 准备数据集
            datasets_to_evaluate = {}
            if self.current_data is not None:
                datasets_to_evaluate['Original'] = self.current_data
            datasets_to_evaluate.update(self.reduced_datasets)

            # 创建性能评估器
            evaluator = PerformanceEvaluator()

            # 评估所有数据集
            self.evaluation_results = evaluator.evaluate_reduction_methods(
                self.current_data if self.current_data is not None else list(self.reduced_datasets.values())[0],
                self.reduced_datasets,
                self.current_target,
                classifier_ensemble
            )

            # 显示结果
            result_text = "分类评估完成:\n\n"

            for method_name, results in self.evaluation_results.items():
                if 'error' in results:
                    result_text += f"{method_name}: 评估失败 - {results['error']}\n\n"
                    continue

                result_text += f"=== {method_name} ===\n"
                result_text += f"特征数: {results['n_features']}\n"
                result_text += f"训练时间: {results['training_time']:.2f}秒\n"

                classification_results = results['classification_results']
                for classifier_name, metrics in classification_results.items():
                    result_text += f"\n{classifier_name}:\n"
                    result_text += f"  准确率: {metrics['accuracy']:.3f}\n"
                    result_text += f"  精确率: {metrics['precision']:.3f}\n"
                    result_text += f"  召回率: {metrics['recall']:.3f}\n"
                    result_text += f"  F1分数: {metrics['f1_score']:.3f}\n"

                result_text += "\n" + "="*50 + "\n\n"

            self.classification_result_text.delete(1.0, tk.END)
            self.classification_result_text.insert(tk.END, result_text)

            messagebox.showinfo("成功", "分类评估完成！")

        except Exception as e:
            messagebox.showerror("错误", f"分类评估失败: {str(e)}")

    def generate_performance_report(self):
        """生成性能报告"""
        if not self.evaluation_results:
            messagebox.showerror("错误", "请先运行分类评估")
            return

        try:
            # 创建性能评估器
            evaluator = PerformanceEvaluator()
            evaluator.evaluation_results = self.evaluation_results

            # 生成详细报告
            detailed_report = evaluator.generate_detailed_report()

            # 创建比较摘要
            summary_df = evaluator.create_comparison_summary()

            # 显示报告
            report_text = "=== 性能评估报告 ===\n\n"

            # 摘要表格
            report_text += "方法比较摘要:\n"
            report_text += summary_df.to_string(index=False) + "\n\n"

            # 推荐建议
            recommendations = detailed_report['recommendations']
            report_text += "推荐建议:\n"
            for i, rec in enumerate(recommendations, 1):
                report_text += f"{i}. {rec}\n"

            report_text += "\n" + "="*80 + "\n\n"

            # 详细结果
            report_text += "详细结果:\n"
            for method_name, results in detailed_report['detailed_results'].items():
                if 'error' in results:
                    continue

                report_text += f"\n{method_name}:\n"
                report_text += f"  特征数: {results['n_features']}\n"
                report_text += f"  约简率: {results['reduction_ratio']:.1%}\n"
                report_text += f"  训练时间: {results['training_time']:.2f}秒\n"
                report_text += f"  最佳分类器: {results['best_classifier']}\n"

                best_perf = results['best_performance']
                report_text += f"  最佳性能:\n"
                report_text += f"    准确率: {best_perf['accuracy']:.3f}\n"
                report_text += f"    精确率: {best_perf['precision']:.3f}\n"
                report_text += f"    召回率: {best_perf['recall']:.3f}\n"
                report_text += f"    F1分数: {best_perf['f1_score']:.3f}\n"

            self.evaluation_result_text.delete(1.0, tk.END)
            self.evaluation_result_text.insert(tk.END, report_text)

            # 同时在结果展示页面显示摘要
            summary_text = "=== 结果摘要 ===\n\n"
            summary_text += summary_df.to_string(index=False) + "\n\n"
            summary_text += "推荐建议:\n"
            for i, rec in enumerate(recommendations, 1):
                summary_text += f"{i}. {rec}\n"

            self.results_summary_text.delete(1.0, tk.END)
            self.results_summary_text.insert(tk.END, summary_text)

            messagebox.showinfo("成功", "性能报告生成完成！")

        except Exception as e:
            messagebox.showerror("错误", f"生成性能报告失败: {str(e)}")

    def export_results(self):
        """导出结果"""
        if not self.evaluation_results:
            messagebox.showerror("错误", "请先运行分类评估")
            return

        try:
            # 选择保存路径
            file_path = filedialog.asksaveasfilename(
                title="导出评估结果",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("CSV files", "*.csv")]
            )

            if not file_path:
                return

            # 创建性能评估器并导出
            evaluator = PerformanceEvaluator()
            evaluator.evaluation_results = self.evaluation_results

            if file_path.endswith('.xlsx'):
                evaluator.export_results(file_path, format='excel')
            else:
                evaluator.export_results(file_path, format='csv')

            messagebox.showinfo("成功", f"结果已导出到: {file_path}")

        except Exception as e:
            messagebox.showerror("错误", f"导出结果失败: {str(e)}")

    def show_performance_chart(self):
        """显示性能对比图"""
        if not self.evaluation_results:
            messagebox.showerror("错误", "请先运行分类评估")
            return

        try:
            evaluator = PerformanceEvaluator()
            evaluator.evaluation_results = self.evaluation_results
            evaluator.plot_performance_comparison()

        except Exception as e:
            messagebox.showerror("错误", f"显示性能图表失败: {str(e)}")

    def show_efficiency_chart(self):
        """显示约简效率图"""
        if not self.evaluation_results:
            messagebox.showerror("错误", "请先运行分类评估")
            return

        try:
            evaluator = PerformanceEvaluator()
            evaluator.evaluation_results = self.evaluation_results
            evaluator.plot_reduction_efficiency()

        except Exception as e:
            messagebox.showerror("错误", f"显示效率图表失败: {str(e)}")
