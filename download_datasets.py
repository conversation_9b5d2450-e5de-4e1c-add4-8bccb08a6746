#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集下载脚本
Dataset Download Script

这个脚本帮助下载推荐的数据集用于属性约简研究
"""

import os
import pandas as pd
import numpy as np
from sklearn.datasets import fetch_openml
import urllib.request
import warnings

warnings.filterwarnings('ignore')

def create_data_directory():
    """创建数据目录"""
    if not os.path.exists('data'):
        os.makedirs('data')
        print("创建data目录")

def download_heart_disease():
    """下载心脏病数据集"""
    try:
        print("正在下载心脏病数据集...")
        
        # 使用OpenML下载
        data = fetch_openml('heart-statlog', version=1, as_frame=True, parser='auto')
        
        # 合并特征和目标
        df = pd.concat([data.data, data.target], axis=1)
        
        # 保存为CSV
        df.to_csv('data/heart_disease.csv', index=False)
        print(f"✅ 心脏病数据集已保存到 data/heart_disease.csv")
        print(f"   样本数: {df.shape[0]}, 特征数: {df.shape[1]-1}")
        
    except Exception as e:
        print(f"❌ 下载心脏病数据集失败: {str(e)}")

def download_diabetes():
    """下载糖尿病数据集"""
    try:
        print("正在下载糖尿病数据集...")
        
        # 从UCI直接下载
        url = "https://raw.githubusercontent.com/jbrownlee/Datasets/master/pima-indians-diabetes.data.csv"
        
        # 下载数据
        urllib.request.urlretrieve(url, 'data/diabetes_temp.csv')
        
        # 读取并添加列名
        column_names = [
            'pregnancies', 'glucose', 'blood_pressure', 'skin_thickness',
            'insulin', 'bmi', 'diabetes_pedigree', 'age', 'outcome'
        ]
        
        df = pd.read_csv('data/diabetes_temp.csv', names=column_names)
        df.to_csv('data/diabetes.csv', index=False)
        
        # 删除临时文件
        os.remove('data/diabetes_temp.csv')
        
        print(f"✅ 糖尿病数据集已保存到 data/diabetes.csv")
        print(f"   样本数: {df.shape[0]}, 特征数: {df.shape[1]-1}")
        
    except Exception as e:
        print(f"❌ 下载糖尿病数据集失败: {str(e)}")

def download_ionosphere():
    """下载电离层数据集"""
    try:
        print("正在下载电离层数据集...")
        
        # 从UCI直接下载
        url = "https://archive.ics.uci.edu/ml/machine-learning-databases/ionosphere/ionosphere.data"
        
        # 下载数据
        urllib.request.urlretrieve(url, 'data/ionosphere_temp.csv')
        
        # 读取数据
        df = pd.read_csv('data/ionosphere_temp.csv', header=None)
        
        # 添加列名
        feature_names = [f'feature_{i+1}' for i in range(df.shape[1]-1)]
        feature_names.append('class')
        df.columns = feature_names
        
        df.to_csv('data/ionosphere.csv', index=False)
        
        # 删除临时文件
        os.remove('data/ionosphere_temp.csv')
        
        print(f"✅ 电离层数据集已保存到 data/ionosphere.csv")
        print(f"   样本数: {df.shape[0]}, 特征数: {df.shape[1]-1}")
        
    except Exception as e:
        print(f"❌ 下载电离层数据集失败: {str(e)}")

def download_sonar():
    """下载声纳数据集"""
    try:
        print("正在下载声纳数据集...")
        
        # 从UCI直接下载
        url = "https://archive.ics.uci.edu/ml/machine-learning-databases/undocumented/connectionist-bench/sonar/sonar.all-data"
        
        # 下载数据
        urllib.request.urlretrieve(url, 'data/sonar_temp.csv')
        
        # 读取数据
        df = pd.read_csv('data/sonar_temp.csv', header=None)
        
        # 添加列名
        feature_names = [f'feature_{i+1}' for i in range(df.shape[1]-1)]
        feature_names.append('class')
        df.columns = feature_names
        
        df.to_csv('data/sonar.csv', index=False)
        
        # 删除临时文件
        os.remove('data/sonar_temp.csv')
        
        print(f"✅ 声纳数据集已保存到 data/sonar.csv")
        print(f"   样本数: {df.shape[0]}, 特征数: {df.shape[1]-1}")
        
    except Exception as e:
        print(f"❌ 下载声纳数据集失败: {str(e)}")

def download_glass():
    """下载玻璃数据集"""
    try:
        print("正在下载玻璃数据集...")
        
        # 从UCI直接下载
        url = "https://archive.ics.uci.edu/ml/machine-learning-databases/glass/glass.data"
        
        # 下载数据
        urllib.request.urlretrieve(url, 'data/glass_temp.csv')
        
        # 读取数据
        df = pd.read_csv('data/glass_temp.csv', header=None)
        
        # 添加列名
        column_names = [
            'id', 'ri', 'na', 'mg', 'al', 'si', 'k', 'ca', 'ba', 'fe', 'glass_type'
        ]
        df.columns = column_names
        
        # 删除ID列
        df = df.drop('id', axis=1)
        
        df.to_csv('data/glass.csv', index=False)
        
        # 删除临时文件
        os.remove('data/glass_temp.csv')
        
        print(f"✅ 玻璃数据集已保存到 data/glass.csv")
        print(f"   样本数: {df.shape[0]}, 特征数: {df.shape[1]-1}")
        
    except Exception as e:
        print(f"❌ 下载玻璃数据集失败: {str(e)}")

def create_sample_excel_files():
    """创建示例Excel文件（包括XLS格式）"""
    try:
        print("正在创建示例Excel文件...")
        
        # 使用iris数据创建示例
        from sklearn.datasets import load_iris
        iris = load_iris()
        
        # 创建DataFrame
        df = pd.DataFrame(iris.data, columns=iris.feature_names)
        df['target'] = iris.target
        
        # 映射目标值到类别名称
        target_names = {0: 'setosa', 1: 'versicolor', 2: 'virginica'}
        df['target'] = df['target'].map(target_names)
        
        # 保存为XLSX格式
        df.to_excel('data/iris_sample.xlsx', index=False, engine='openpyxl')
        print(f"✅ 示例XLSX文件已保存到 data/iris_sample.xlsx")
        
        # 保存为XLS格式（如果xlwt可用）
        try:
            import xlwt
            # 对于XLS格式，需要使用xlwt引擎，但pandas可能不支持
            # 我们手动创建XLS文件
            workbook = xlwt.Workbook()
            worksheet = workbook.add_sheet('Sheet1')

            # 写入列名
            for col_idx, col_name in enumerate(df.columns):
                worksheet.write(0, col_idx, col_name)

            # 写入数据
            for row_idx, row in df.iterrows():
                for col_idx, value in enumerate(row):
                    worksheet.write(row_idx + 1, col_idx, value)

            workbook.save('data/iris_sample.xls')
            print(f"✅ 示例XLS文件已保存到 data/iris_sample.xls")
        except ImportError:
            print("⚠️  xlwt未安装，无法创建XLS文件。运行: pip install xlwt")
        except Exception as e:
            print(f"⚠️  创建XLS文件失败: {str(e)}")
        
    except Exception as e:
        print(f"❌ 创建示例Excel文件失败: {str(e)}")

def main():
    """主函数"""
    print("=" * 60)
    print("数据集下载工具")
    print("=" * 60)
    
    # 创建数据目录
    create_data_directory()
    
    print("\n开始下载数据集...")
    print("-" * 40)
    
    # 下载各种数据集
    download_heart_disease()
    download_diabetes()
    download_ionosphere()
    download_sonar()
    download_glass()
    
    print("\n创建示例Excel文件...")
    print("-" * 40)
    create_sample_excel_files()
    
    print("\n" + "=" * 60)
    print("下载完成！")
    print("=" * 60)
    
    # 显示下载的文件列表
    print("\n已下载的数据集:")
    data_files = []
    if os.path.exists('data'):
        for file in os.listdir('data'):
            if file.endswith(('.csv', '.xlsx', '.xls')):
                file_path = os.path.join('data', file)
                file_size = os.path.getsize(file_path)
                data_files.append((file, file_size))
    
    if data_files:
        for file, size in sorted(data_files):
            print(f"  📁 {file} ({size/1024:.1f} KB)")
    else:
        print("  没有找到数据文件")
    
    print(f"\n使用方法:")
    print(f"1. 启动GUI: python main.py")
    print(f"2. 在'数据处理'标签页中点击'浏览'按钮")
    print(f"3. 选择data目录中的任意数据文件")
    print(f"4. 开始您的属性约简研究！")

if __name__ == "__main__":
    main()
