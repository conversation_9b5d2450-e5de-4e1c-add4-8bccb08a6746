#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件对话框
Test File Dialog
"""

import tkinter as tk
from tkinter import filedialog
import os

def test_file_dialog():
    """测试文件选择对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    print("测试文件选择对话框...")
    print("请在弹出的对话框中导航到data目录，查看是否能看到所有文件类型")
    
    file_path = filedialog.askopenfilename(
        title="选择数据文件 - 测试XLS文件可见性",
        initialdir="./data" if os.path.exists("./data") else ".",
        filetypes=[
            ("CSV files", "*.csv"), 
            ("Excel XLSX files", "*.xlsx"), 
            ("Excel XLS files", "*.xls"),
            ("All files", "*.*")
        ]
    )
    
    if file_path:
        print(f"✅ 选择的文件: {file_path}")
        file_ext = file_path.lower().split('.')[-1]
        print(f"文件扩展名: {file_ext}")
        
        if file_ext in ['csv', 'xlsx', 'xls']:
            print(f"✅ 文件格式受支持")
        else:
            print(f"⚠️  文件格式可能不受支持")
    else:
        print("❌ 未选择文件")
    
    root.destroy()

if __name__ == "__main__":
    # 首先检查data目录中的文件
    print("data目录中的文件:")
    if os.path.exists("data"):
        for file in os.listdir("data"):
            if file.endswith(('.csv', '.xlsx', '.xls')):
                print(f"  📁 {file}")
    else:
        print("  data目录不存在")
    
    print("\n" + "="*50)
    test_file_dialog()
